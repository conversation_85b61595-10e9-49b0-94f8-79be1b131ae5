@import "../../styles/_variables.scss";

.heroSection {
  width: 100%;
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;

  .pageTitle {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0;
  }
}

.checkout {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: $color-bg-light;
  padding-top: $spacing-4xl;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0 0 $spacing-4xl 0;
    text-align: center;
  }

  .container {
    max-width: 1366px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: $spacing-4xl;
    padding: 0 2rem;
  }

  section {
    background: $color-bg-white;
    border-radius: $border-radius-5xl;
    box-shadow: $shadow-sm;
    padding: $spacing-2xl;
  }

  .orderSummary {
    h2 {
      margin-bottom: $spacing-md;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .itemsList {
      display: flex;
      flex-direction: column;

      margin: 0;
      padding: 0;

      .item {
        display: flex;
        justify-content: space-between;
        margin-bottom: $spacing-sm;
        color: $color-text-secondary;
        font-family: $font-secondary;

        .itemImage {
          width: 80px;
          height: 80px;
          border-radius: $border-radius-lg;
          object-fit: cover;
          transition: $transition-fast;

          &:hover {
            transform: scale(0.99);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .total {
      display: flex;
      justify-content: space-between;
      font-weight: $font-weight-semibold;
      margin-top: $spacing-md;
      font-family: $font-secondary;
    }
  }

  .shippingForm {
    h2 {
      margin-bottom: $spacing-md;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .form {
      display: flex;
      flex-direction: column;

      label {
        margin-bottom: $spacing-xs;
        font-weight: $font-weight-medium;
        color: $color-text-primary;
        font-family: $font-secondary;
      }

      input {
        margin-bottom: $spacing-md;
        padding: $spacing-sm;
        border: 1px solid $color-border-light;
        border-radius: $border-radius-sm;
        font-family: $font-secondary;
      }

      .submitBtn {
        margin-top: $spacing-md;
        padding: $spacing-sm $spacing-md;
        background: $color-primary;
        color: $color-bg-white;
        border: none;
        border-radius: $border-radius-sm;
        cursor: pointer;
        font-family: $font-secondary;
        font-weight: $font-weight-semibold;
        transition: background $transition-fast;

        &:hover {
          background: $color-primary-hover;
        }
      }
    }
  }

  @media (max-width: $breakpoint-tablet) {
    .container {
      grid-template-columns: 1fr;
    }
  }
}

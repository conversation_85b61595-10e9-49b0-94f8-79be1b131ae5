@import "../../styles/variables";

.cartPage {
  width: 100%;
  max-width: 100vw;
  background-color: $color-bg-white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.heroSection {
  width: 100%;
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;

  @media (max-width: $breakpoint-tablet) {
    padding: $spacing-2xl 1rem;
  }

  @media (max-width: $breakpoint-mobile) {
    padding: $spacing-xl 1rem;
  }
}

.pageTitle {
  font-family: $font-primary;
  font-size: $font-size-hero-md;
  font-weight: $font-weight-extra-bold;
  color: $color-secondary;
  margin: 0;
}

.mainContent {
  flex: 1;
  background-color: $color-bg-light;
  padding: $spacing-4xl 2rem;

  @media (max-width: $breakpoint-tablet) {
    padding: $spacing-2xl 1rem;
  }

  @media (max-width: $breakpoint-mobile) {
    padding: $spacing-xl 1rem;
  }
}

.container {
  max-width: 1366px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: $spacing-4xl;
}

.cartSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.cartHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 60px;
  gap: $spacing-md;
  padding: $spacing-md $spacing-lg;
  background-color: $color-bg-light;
  border-radius: $border-radius-lg;
  font-family: $font-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $color-text-secondary;
  text-align: center;

  .productColumn {
    text-align: left;
  }
}

.cartItem {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 60px;
  gap: $spacing-md;
  align-items: center;
  background-color: $color-bg-white;
  border-radius: $border-radius-5xl;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;
  transition: $transition-fast;

  &:hover {
    box-shadow: $shadow-md;
  }
}

.productInfo {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.imageContainer {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.itemImage {
  width: 80px;
  height: 80px;
  border-radius: $border-radius-lg;
  object-fit: cover;
  transition: $transition-fast;

  &:hover {
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

.productDetails {
  flex: 1;
}

.itemTitle {
  font-family: $font-primary;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $color-text-primary;
  margin: 0;
  line-height: 1.3;
}

.priceInfo,
.quantityInfo,
.totalInfo,
.actionInfo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.itemPrice,
.itemTotal {
  font-family: $font-secondary;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $color-text-primary;
}

.itemTotal {
  font-weight: $font-weight-semibold;
  color: $color-secondary;
}

.quantityInput {
  width: 60px;
  height: 40px;
  border: 1px solid $color-border-light;
  border-radius: $border-radius-md;
  padding: 0.5rem;
  font-size: $font-size-base;
  font-family: $font-secondary;
  text-align: center;
  transition: $transition-fast;

  &:focus {
    outline: none;
    border-color: $color-border-focus;
    box-shadow: 0 0 0 2px rgba(219, 152, 196, 0.2);
  }

  &:hover {
    border-color: $color-border-focus;
  }
}

.removeBtn {
  background: transparent;
  border: none;
  color: $color-danger;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: $border-radius-md;
  transition: $transition-fast;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(220, 85, 96, 0.1);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.orderSummary {
  background-color: $color-bg-white;
  padding: $spacing-2xl;
  border-radius: $border-radius-5xl;
  box-shadow: $shadow-sm;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.summaryTitle {
  font-family: $font-primary;
  font-size: $font-size-2xl;
  font-weight: $font-weight-semibold;
  margin: 0 0 $spacing-md 0;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  font-family: $font-secondary;
  font-size: $font-size-base;
  color: $color-text-primary;

  & strong {
    font-family: $font-primary;
  }
}

.checkoutBtn {
  background-color: $color-bg-button-secondary;
  color: $color-text-primary;
  border: none;
  border-radius: $border-radius-5xl;
  padding: $spacing-md;
  font-family: $font-primary;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  cursor: pointer;
  transition: $transition-fast;

  &:hover {
    background-color: $color-primary-hover;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

.emptyMessage {
  font-family: $font-secondary;
  font-size: $font-size-lg;
  color: $color-text-muted;
  text-align: center;
  padding: $spacing-2xl 0;
}

// Responsive Design
// Large Desktop (1200px+)
@media (min-width: $breakpoint-desktop) {
  .container {
    gap: $spacing-4xl;
  }

  .cartHeader {
    padding: $spacing-lg $spacing-2xl;
  }

  .cartItem {
    padding: $spacing-2xl;
  }

  .itemImage {
    width: 100px;
    height: 100px;
  }

  .imageContainer {
    width: 100px;
    height: 100px;
  }
}

// Tablet (768px - 1199px)
@media (max-width: $breakpoint-desktop) and (min-width: $breakpoint-tablet) {
  .container {
    gap: $spacing-2xl;
    padding: 0 $spacing-md;
  }

  .cartHeader {
    grid-template-columns: 2fr 0.8fr 0.8fr 0.8fr 50px;
    font-size: $font-size-sm;
    padding: $spacing-sm $spacing-md;
  }

  .cartItem {
    grid-template-columns: 2fr 0.8fr 0.8fr 0.8fr 50px;
    padding: $spacing-md;
  }

  .itemImage {
    width: 70px;
    height: 70px;
  }

  .imageContainer {
    width: 70px;
    height: 70px;
  }

  .itemTitle {
    font-size: $font-size-base;
  }

  .quantityInput {
    width: 50px;
    height: 35px;
  }
}

// Mobile and Small Tablet (up to 767px)
@media (max-width: $breakpoint-tablet) {
  .container {
    grid-template-columns: 1fr;
    gap: $spacing-2xl;
    padding: 0;
  }

  .cartHeader {
    display: none; // Hide header on mobile for cleaner look
  }

  .cartItem {
    grid-template-columns: 1fr;
    gap: $spacing-sm;
    padding: $spacing-md;
    border-radius: $border-radius-lg;
  }

  .productInfo {
    justify-content: flex-start;
    gap: $spacing-sm;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $color-border-light;
  }

  .imageContainer {
    width: 60px;
    height: 60px;
  }

  .itemImage {
    width: 60px;
    height: 60px;
  }

  .itemTitle {
    font-size: $font-size-base;
  }

  .priceInfo,
  .quantityInfo,
  .totalInfo {
    justify-content: space-between;
    padding: $spacing-xs 0;
    border-top: 1px solid $color-border-light;

    &::before {
      font-family: $font-secondary;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $color-text-muted;
    }
  }

  .priceInfo::before {
    content: "Price:";
  }

  .quantityInfo::before {
    content: "Qty:";
  }

  .totalInfo::before {
    content: "Total:";
  }

  .actionInfo {
    justify-content: center;
    padding-top: $spacing-sm;
    border-top: 1px solid $color-border-light;
  }

  .quantityInput {
    width: 80px;
    height: 40px;
  }

  .orderSummary {
    padding: $spacing-lg;
    position: sticky;
    bottom: 0;
    z-index: $z-index-elevated;
  }
}

// Small Mobile (up to 480px)
@media (max-width: $breakpoint-mobile) {
  .heroSection {
    padding: $spacing-2xl 1rem;
  }

  .pageTitle {
    font-size: $font-size-hero-sm;
  }

  .mainContent {
    padding: $spacing-2xl 1rem;
  }

  .cartItem {
    padding: $spacing-sm;
  }

  .productInfo {
    gap: $spacing-xs;
  }

  .imageContainer {
    width: 50px;
    height: 50px;
  }

  .itemImage {
    width: 50px;
    height: 50px;
  }

  .itemTitle {
    font-size: $font-size-sm;
    line-height: 1.2;
  }

  .quantityInput {
    width: 70px;
    height: 35px;
    font-size: $font-size-sm;
  }

  .orderSummary {
    padding: $spacing-md;
    gap: $spacing-md;
  }

  .summaryTitle {
    font-size: $font-size-lg;
  }

  .checkoutBtn {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-base;
  }

  .emptyMessage {
    font-size: $font-size-base;
    padding: $spacing-xl 0;
  }
}

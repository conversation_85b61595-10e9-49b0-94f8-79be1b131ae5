import React, { useState } from 'react';
import styles from './CheckoutPage.module.scss';
import type { NotificationsProps } from '../Notifications/Notification';
import type { CartItem } from '../CartPage/CartItem';
import UseCart from '../CartPage/CartActions';
import Navigation from '../Navigation';
import Footer from '../Footer';

const CheckoutPage: React.FC<NotificationsProps> = ({ notifications, setNotifications }) => {
    // get cart items and actions
    const { getCartItems, clearCart } = UseCart({ notifications, setNotifications });
    const cartItems: CartItem[] = getCartItems();
    const total = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);

    // shipping form state
    const [shippingInfo, setShippingInfo] = useState({
        name: '',
        address: '',
        city: '',
        zip: '',
        country: ''
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setShippingInfo(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // normally send order to API
        setNotifications([...notifications, { id: Date.now(), message: 'Order placed successfully!' }]);
        clearCart();
    };

    return (
        <>
            <Navigation />

            <section className={styles.heroSection}>
                <h1 className={styles.pageTitle}>Checkout</h1>
            </section>

            <div className={styles.checkout}>
                <div className={styles.container}>
                    <section className={styles.orderSummary}>
                        <h2>Your Order</h2>
                        {cartItems.length === 0 ? (
                            <p>Your cart is empty.</p>
                        ) : (
                            <ul className={styles.itemsList}>
                                {cartItems.map(item => (
                                    <li key={item.id} className={styles.item}>
                                        <span>{item.name} x{item.quantity}</span>
                                        <span>${(item.price * item.quantity).toFixed(2)}</span>
                                    </li>
                                ))}
                            </ul>
                        )}
                        <div className={styles.total}>
                            <span>Total:</span>
                            <span>${total.toFixed(2)}</span>
                        </div>
                    </section>

                    <section className={styles.shippingForm}>
                        <h2>Shipping Information</h2>
                        <form onSubmit={handleSubmit} className={styles.form}>
                            <label htmlFor="name">Name</label>
                            <input id="name" name="name" value={shippingInfo.name} onChange={handleChange} required />

                            <label htmlFor="address">Address</label>
                            <input id="address" name="address" value={shippingInfo.address} onChange={handleChange} required />

                            <label htmlFor="city">City</label>
                            <input id="city" name="city" value={shippingInfo.city} onChange={handleChange} required />

                            <label htmlFor="zip">Postal Code</label>
                            <input id="zip" name="zip" value={shippingInfo.zip} onChange={handleChange} required />

                            <label htmlFor="country">Country</label>
                            <input id="country" name="country" value={shippingInfo.country} onChange={handleChange} required />

                            <button type="submit" className={styles.submitBtn}>Place Order</button>
                        </form>
                    </section>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default CheckoutPage;
